<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_112) on Thu Jun 14 23:06:37 CST 2018 -->
<title>Class1Remote</title>
<meta name="date" content="2018-06-14">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Class1Remote";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../Test/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../Test/Class1.html" title="class in Test"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../Test/TestMCRFactory.html" title="class in Test"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?Test/Class1Remote.html" target="_top">Frames</a></li>
<li><a href="Class1Remote.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">Test</div>
<h2 title="Interface Class1Remote" class="title">Interface Class1Remote</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd>com.mathworks.toolbox.javabuilder.pooling.Poolable, java.rmi.Remote</dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">Class1Remote</span>
extends com.mathworks.toolbox.javabuilder.pooling.Poolable</pre>
<div class="block">The <code>Class1Remote</code> class provides a Java RMI-compliant interface to the 
 M-functions from the files:
 <pre>
  /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest.m
 </pre>
 The <a href="../Test/Class1Remote.html#dispose--"><code>dispose()</code></a> method <b>must</b> be called on a <code>Class1Remote</code> 
 instance when it is no longer needed to ensure that native resources allocated by this 
 class are properly freed, and the server-side proxy is unexported.  (Failure to call 
 dispose may result in server-side threads not being properly shut down, which often 
 appears as a hang.)  

 This interface is designed to be used together with 
 <code>com.mathworks.toolbox.javabuilder.remoting.RemoteProxy</code> to automatically 
 generate RMI server proxy objects for instances of Test.Class1.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../Test/Class1Remote.html#dispose--">dispose</a></span>()</code>
<div class="block">Frees native resources associated with the remote server object</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.Object[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../Test/Class1Remote.html#fortest-int-java.lang.Object...-">fortest</a></span>(int&nbsp;nargout,
       java.lang.Object...&nbsp;rhs)</code>
<div class="block">Provides the standard interface for calling the <code>fortest</code> M-function 
 with 2 input arguments.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.mathworks.toolbox.javabuilder.pooling.Poolable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.mathworks.toolbox.javabuilder.pooling.Poolable</h3>
<code>alive</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="fortest-int-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fortest</h4>
<pre>java.lang.Object[]&nbsp;fortest(int&nbsp;nargout,
                           java.lang.Object...&nbsp;rhs)
                    throws java.rmi.RemoteException</pre>
<div class="block">Provides the standard interface for calling the <code>fortest</code> M-function 
 with 2 input arguments.  

 Input arguments to standard interface methods may be passed as sub-classes of 
 <code>com.mathworks.toolbox.javabuilder.MWArray</code>, or as arrays of any 
 supported Java type (i.e. scalars and multidimensional arrays of any numeric, 
 boolean, or character type, or String). Arguments passed as Java types are 
 converted to MATLAB arrays according to default conversion rules.

 All inputs to this method must implement either Serializable (pass-by-value) or 
 Remote (pass-by-reference) as per the RMI specification.

 No usage documentation is available for this function.  (To fix this, the function 
 author should insert a help comment at the beginning of their M code.  See the 
 MATLAB documentation for more details.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nargout</code> - Number of outputs to return.</dd>
<dd><code>rhs</code> - The inputs to the M function.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Array of length nargout containing the function outputs. Outputs are 
 returned as sub-classes of <code>com.mathworks.toolbox.javabuilder.MWArray</code>. 
 Each output array should be freed by calling its <code>dispose()</code> method.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.jmi.RemoteException</code> - An error has occurred during the function call or 
 in communication with the server.</dd>
<dd><code>java.rmi.RemoteException</code></dd>
</dl>
</li>
</ul>
<a name="dispose--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>dispose</h4>
<pre>void&nbsp;dispose()
      throws java.rmi.RemoteException</pre>
<div class="block">Frees native resources associated with the remote server object</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.rmi.RemoteException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../Test/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../Test/Class1.html" title="class in Test"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../Test/TestMCRFactory.html" title="class in Test"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?Test/Class1Remote.html" target="_top">Frames</a></li>
<li><a href="Class1Remote.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
