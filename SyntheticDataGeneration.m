boundary = 100; % side length of the map.
param.axisRange = [0.5, boundary - 0.5 ,0.5, boundary - 0.5];

numbofSynthesisPoints = 200; % number of points
numbofKeyPoints = 20;  % number of keypoints
numbofSynTraj = 50;  % number of trajectory


if numbofKeyPoints >= numbofSynTraj
    error('k should be smaller than T');
end

points = (boundary-1) * rand(numbofSynthesisPoints,2) + 0.5;

startInd = ceil(numbofSynthesisPoints * rand(numbofSynTraj,1));
endInd = ceil(numbofSynthesisPoints * rand(numbofSynTraj,1));
keyInd = startInd(1:numbofKeyPoints);

keypoints = points(keyInd,:);
startPoints = points(startInd,:);
endPoints = points(endInd,:);


%%

% 简化版本：直接生成路径而不使用robotics工具箱
% 这是为了Octave兼容性而做的简化

latLong = [];

for i = 1:numbofSynTraj
    startLocation = startPoints(i,:);
    endLocation = endPoints(i,:);

    % 简单的直线路径生成（替代PRM路径规划）
    numPathPoints = randi([15, 25]); % 随机路径点数量

    % 生成从起点到终点的路径
    t = linspace(0, 1, numPathPoints)';
    path = [startLocation(1) + t * (endLocation(1) - startLocation(1)), ...
            startLocation(2) + t * (endLocation(2) - startLocation(2))];

    % 添加一些随机噪声使路径更真实
    noise = randn(size(path)) * 2; % 噪声强度
    path = path + noise;

    % 确保路径点在边界内
    path(:,1) = max(0.5, min(boundary-0.5, path(:,1)));
    path(:,2) = max(0.5, min(boundary-0.5, path(:,2)));

    fprintf('iteration[%d] = generated path with %d points\n', i, size(path,1));
    latLong = [latLong; [i * ones(size(path,1),1), path]];
end

%% For Trajectory Retrieval paper
% param.numberOfVisualization = numbofSynTraj;
% param.visualStyle = 'plot';
% param.axisRange = [0.5, boundary - 0.5 ,0.5, boundary - 0.5];
% visualization(latLong,param);
% 
% 
% 
% hold on;
% plot(keypoints(:,2),keypoints(:,1),'p','MarkerSize',25,'MarkerFaceColor','[.6 .6 .6]','Color',[0 0 0],'LineWidth',2);
% a = gca;
% axis(a,'square')
% set(a,'XTick',[],'XTickLabel',[]);
% set(a,'YTick',[],'YTickLabel',[]);
% box on;
% a.Position = [0 0 1 1];
% f = gcf;
% pos =  f.Position;
% if pos(4) < pos(3)
%     pos(3) = pos(4);
% end
% f.Position = pos;
% hold off;

%% For Trajectory Compression
param.numberOfVisualization = numbofSynTraj;
param.visualStyle = 'plot';
param.axisRange = [0.5, boundary - 0.5 ,0.5, boundary - 0.5];
visualization(latLong,param);


hold on;
% 修复颜色格式以兼容Octave
plot(keypoints(:,2),keypoints(:,1),'^','MarkerSize',15,'MarkerFaceColor',[0.5660 0.7740 0.2880],'Color',[0 0 0],'LineWidth',2);
a = gca;
axis(a,'square')
set(a,'XTick',[],'XTickLabel',[]);
set(a,'YTick',[],'YTickLabel',[]);
box on;
% Octave兼容的位置设置
set(a, 'Position', [0 0 1 1]);
f = gcf;
pos = get(f, 'Position');
if pos(4) < pos(3)
    pos(3) = pos(4);
end
set(f, 'Position', pos);
hold off;
