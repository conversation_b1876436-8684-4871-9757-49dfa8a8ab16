#!/usr/bin/env python3
"""
Semantic Trajectory Mining Demo
A simplified Python implementation demonstrating the core concepts of the MATLAB project.
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from sklearn.cluster import DBSCAN
from sklearn.neighbors import NearestNeighbors
from sklearn.decomposition import PCA
from sklearn.metrics.pairwise import euclidean_distances
import random
from typing import List, Tuple, Dict
import warnings
warnings.filterwarnings('ignore')

class SyntheticDataGenerator:
    """Generate synthetic trajectory data similar to SyntheticDataGeneration.m"""
    
    def __init__(self, boundary=100, num_points=200, num_keypoints=20, num_trajectories=50):
        self.boundary = boundary
        self.num_points = num_points
        self.num_keypoints = num_keypoints
        self.num_trajectories = num_trajectories
        self.axis_range = [0.5, boundary - 0.5, 0.5, boundary - 0.5]
        
    def generate_trajectories(self):
        """Generate synthetic trajectory data"""
        print(f"Generating {self.num_trajectories} synthetic trajectories...")
        
        # Generate random points
        points = np.random.rand(self.num_points, 2) * (self.boundary - 1) + 0.5
        
        # Select start and end points for trajectories
        start_indices = np.random.randint(0, self.num_points, self.num_trajectories)
        end_indices = np.random.randint(0, self.num_points, self.num_trajectories)
        
        # Select keypoints
        key_indices = start_indices[:self.num_keypoints]
        keypoints = points[key_indices]
        
        trajectories = []
        
        for i in range(self.num_trajectories):
            start_point = points[start_indices[i]]
            end_point = points[end_indices[i]]
            
            # Generate a simple path between start and end points
            num_steps = np.random.randint(10, 30)
            path = self.generate_path(start_point, end_point, num_steps)
            
            # Add trajectory ID to each point
            traj_data = np.column_stack([
                np.full(len(path), i + 1),  # trajectory ID
                path[:, 1],  # latitude
                path[:, 0]   # longitude
            ])
            
            trajectories.append(traj_data)
        
        # Combine all trajectories
        lat_long = np.vstack(trajectories)
        
        print(f"Generated {len(lat_long)} GPS points across {self.num_trajectories} trajectories")
        return lat_long, keypoints
    
    def generate_path(self, start, end, num_steps):
        """Generate a simple path between two points with some randomness"""
        # Linear interpolation with noise
        t = np.linspace(0, 1, num_steps)
        path = np.outer(1 - t, start) + np.outer(t, end)
        
        # Add some random noise to make paths more realistic
        noise = np.random.normal(0, 0.5, path.shape)
        path += noise
        
        # Ensure points stay within boundary
        path = np.clip(path, 0.5, self.boundary - 0.5)
        
        return path

class FastHierarchicalSynchronization:
    """Simplified version of Fast_Hierarchical_Synchronization.m"""
    
    def __init__(self, knn=10, epsilon=0.02, max_displacement=0.001):
        self.knn = knn
        self.epsilon = epsilon * np.pi / 2
        self.max_displacement = max_displacement * np.pi / 2
        
    def normalize_data(self, X, axis_range):
        """Normalize data to [0, pi/2] range"""
        X_norm = np.copy(X)
        X_norm[:, 0] = (X[:, 0] - axis_range[2]) / (axis_range[3] - axis_range[2]) * np.pi / 2
        X_norm[:, 1] = (X[:, 1] - axis_range[0]) / (axis_range[1] - axis_range[0]) * np.pi / 2
        return X_norm
    
    def synchronize(self, X, axis_range):
        """Perform hierarchical synchronization clustering"""
        print("Performing Fast Hierarchical Synchronization...")
        
        if len(X) == 0:
            raise ValueError("No data input")
        
        # Normalize data
        X_norm = self.normalize_data(X, axis_range)
        
        # Use DBSCAN for clustering (simplified version of the hierarchical approach)
        clustering = DBSCAN(eps=self.epsilon, min_samples=self.knn)
        cluster_labels = clustering.fit_predict(X_norm)
        
        # Find prototype points (cluster centers)
        unique_labels = np.unique(cluster_labels)
        prototypes = []
        weights = []
        
        for label in unique_labels:
            if label == -1:  # noise points
                continue
            
            cluster_points = X_norm[cluster_labels == label]
            # Use centroid as prototype
            prototype = np.mean(cluster_points, axis=0)
            weight = len(cluster_points)
            
            prototypes.append(prototype)
            weights.append(weight)
        
        prototypes = np.array(prototypes)
        weights = np.array(weights)
        
        # Create mapping from original points to prototypes
        nn = NearestNeighbors(n_neighbors=1)
        nn.fit(prototypes)
        distances, indices = nn.kneighbors(X_norm)
        
        result = {
            'X': prototypes,
            'weight': weights,
            'numberOfPrototypes': len(prototypes),
            'Map_Original_To_Prototype': indices.flatten()
        }
        
        print(f"Found {len(prototypes)} prototype points")
        return result

class TrajectoryEmbedding:
    """Simplified trajectory embedding similar to AsymmetricSPEmbedding.m"""
    
    def __init__(self, dim=50):
        self.dim = dim
    
    def embed_trajectories(self, lat_long_prototypes, sync_result):
        """Create trajectory embeddings"""
        print("Creating trajectory embeddings...")
        
        # Get unique trajectory IDs
        traj_ids = np.unique(lat_long_prototypes[:, 0])
        
        # Create simple embeddings based on prototype sequences
        embeddings = []
        
        for traj_id in traj_ids:
            traj_points = lat_long_prototypes[lat_long_prototypes[:, 0] == traj_id]
            
            # Get prototype indices for this trajectory
            prototype_indices = sync_result['Map_Original_To_Prototype'][
                lat_long_prototypes[:, 0] == traj_id
            ]
            
            # Create a simple embedding based on prototype visitation
            embedding = np.zeros(self.dim)
            
            # Use prototype indices to create features
            for i, proto_idx in enumerate(prototype_indices):
                if proto_idx < self.dim:
                    embedding[proto_idx] += 1
            
            # Normalize
            if np.sum(embedding) > 0:
                embedding = embedding / np.sum(embedding)
            
            embeddings.append(embedding)
        
        embeddings = np.array(embeddings)
        print(f"Created embeddings for {len(embeddings)} trajectories")
        return embeddings, traj_ids

class Visualizer:
    """Visualization utilities similar to visualization.m"""
    
    @staticmethod
    def plot_trajectories(lat_long, axis_range, title="Trajectories"):
        """Plot trajectory data"""
        plt.figure(figsize=(10, 8))
        
        # Get unique trajectory IDs
        traj_ids = np.unique(lat_long[:, 0])
        colors = plt.cm.tab10(np.linspace(0, 1, len(traj_ids)))
        
        for i, traj_id in enumerate(traj_ids):
            traj_points = lat_long[lat_long[:, 0] == traj_id]
            plt.plot(traj_points[:, 2], traj_points[:, 1], 
                    color=colors[i], alpha=0.7, linewidth=1)
        
        plt.xlim(axis_range[0], axis_range[1])
        plt.ylim(axis_range[2], axis_range[3])
        plt.xlabel('Longitude')
        plt.ylabel('Latitude')
        plt.title(title)
        plt.grid(True, alpha=0.3)
        return plt.gcf()
    
    @staticmethod
    def plot_prototypes(prototypes, weights, axis_range, title="Prototype Points"):
        """Plot prototype points with weights"""
        plt.figure(figsize=(10, 8))
        
        # Denormalize prototypes for plotting
        proto_denorm = np.copy(prototypes)
        proto_denorm[:, 1] = prototypes[:, 1] / (np.pi/2) * (axis_range[1] - axis_range[0]) + axis_range[0]
        proto_denorm[:, 0] = prototypes[:, 0] / (np.pi/2) * (axis_range[3] - axis_range[2]) + axis_range[2]
        
        # Plot with size proportional to weight
        sizes = weights / np.max(weights) * 200 + 20
        scatter = plt.scatter(proto_denorm[:, 1], proto_denorm[:, 0], 
                            s=sizes, c=weights, cmap='viridis', alpha=0.7)
        
        plt.colorbar(scatter, label='Weight')
        plt.xlim(axis_range[0], axis_range[1])
        plt.ylim(axis_range[2], axis_range[3])
        plt.xlabel('Longitude')
        plt.ylabel('Latitude')
        plt.title(title)
        plt.grid(True, alpha=0.3)
        return plt.gcf()

def main():
    """Main demonstration function"""
    print("=== Semantic Trajectory Mining Demo ===")
    print("This is a simplified Python implementation of the MATLAB project.")
    print()
    
    # Step 1: Generate synthetic data
    print("Step 1: Generating synthetic trajectory data...")
    generator = SyntheticDataGenerator(boundary=100, num_trajectories=20)
    lat_long, keypoints = generator.generate_trajectories()
    
    # Step 2: Visualize original trajectories
    print("\nStep 2: Visualizing original trajectories...")
    fig1 = Visualizer.plot_trajectories(lat_long, generator.axis_range, 
                                       "Original Synthetic Trajectories")
    plt.show()
    
    # Step 3: Perform hierarchical synchronization
    print("\nStep 3: Performing hierarchical synchronization...")
    sync = FastHierarchicalSynchronization(knn=5, epsilon=0.05)
    sync_result = sync.synchronize(lat_long[:, [1, 2]], generator.axis_range)
    
    # Step 4: Visualize prototype points
    print("\nStep 4: Visualizing prototype points...")
    fig2 = Visualizer.plot_prototypes(sync_result['X'], sync_result['weight'], 
                                     generator.axis_range, "Prototype Points (SPs)")
    plt.show()
    
    # Step 5: Create trajectory embeddings
    print("\nStep 5: Creating trajectory embeddings...")
    embedder = TrajectoryEmbedding(dim=30)
    embeddings, traj_ids = embedder.embed_trajectories(lat_long, sync_result)
    
    # Step 6: Visualize embeddings using PCA
    print("\nStep 6: Visualizing trajectory embeddings...")
    if len(embeddings) > 1:
        pca = PCA(n_components=2)
        embeddings_2d = pca.fit_transform(embeddings)
        
        plt.figure(figsize=(10, 8))
        scatter = plt.scatter(embeddings_2d[:, 0], embeddings_2d[:, 1], 
                            c=range(len(embeddings)), cmap='tab10', s=100)
        plt.colorbar(scatter, label='Trajectory ID')
        plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} variance)')
        plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} variance)')
        plt.title('Trajectory Embeddings (PCA Visualization)')
        plt.grid(True, alpha=0.3)
        plt.show()
    
    print("\n=== Demo completed successfully! ===")
    print(f"Processed {len(lat_long)} GPS points from {len(traj_ids)} trajectories")
    print(f"Found {sync_result['numberOfPrototypes']} prototype points")
    print(f"Created {len(embeddings)}-dimensional embeddings")

if __name__ == "__main__":
    main()
