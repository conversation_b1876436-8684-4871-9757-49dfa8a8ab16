# MATLAB项目完整运行指南

## 环境要求

### 1. MATLAB安装
需要安装MATLAB R2018a或更高版本，包含以下工具箱：
- Statistics and Machine Learning Toolbox
- Image Processing Toolbox  
- Robotics System Toolbox
- Optimization Toolbox

### 2. 数据准备
项目支持以下数据集：

#### GeoLife数据集
- 下载地址：https://www.microsoft.com/en-us/download/details.aspx?id=52367
- 解压到 `Data/GeoLife/` 目录

#### T-Drive数据集  
- 下载地址：https://www.microsoft.com/en-us/research/publication/t-drive-trajectory-data-sample/
- 解压到 `Data/T_Drive/` 目录

#### Kaggle出租车数据集
- 下载地址：https://www.kaggle.com/c/pkdd-15-predict-taxi-service-trajectory-i
- 将train.csv放到 `Data/Kaggle/` 目录

## 完整运行步骤

### 步骤1：合成数据实验
```matlab
% 在MATLAB命令窗口运行
SyntheticExperiment;
```

### 步骤2：真实数据实验
```matlab
% 编辑 expriment_realworld_data.m 文件
% 选择要使用的数据集（取消注释相应部分）

% 例如使用GeoLife数据集：
param.inputFile = 'Data/GeoLife';
param.readingNumOfTraj = 100000;
param.imgMap = 'Data/beijing1.png';
param.axisRange = [116.1935 116.5531 39.7513 40.0334];

% 运行实验
expriment_realworld_data;
```

### 步骤3：轨迹压缩实验
```matlab
cd TrajectoryCompression
Script_Hierarchical_Sync;
```

## 核心功能模块

### 1. 快速分层同步算法
```matlab
% 主要参数
param.Knn = 10;              % 最近邻数量
param.epsilon = 0.01;        % 交互距离
param.maxLayer = 20;         % 最大层数

% 调用算法
FastSyncResult = Fast_Hierarchical_Synchronization(latLong(:,[2,3]), param);
```

### 2. 轨迹嵌入
```matlab
% 嵌入参数
param.dim = 50;              % 嵌入维度
param.learningRate = 0.025;  % 学习率
param.window = 2;            % 窗口大小

% 生成嵌入
[S, T, ~] = AsymmetricSPEmbedding(SP_pair_indeces, latLong_using_prototypes_index, param);
```

### 3. 多种距离度量
```matlab
% DTW距离
[~, TopK_record_DTW] = Tra_distance_matrix_using_dtw_index_randomK(latLong_using_prototypes_index, FastSyncResult.X, randnum, num);

% Frechet距离  
[~, TopK_record_Frechet] = Tra_distance_matrix_using_Frechet_index_randomK(latLong_using_prototypes_index, FastSyncResult.X, randnum, num);

% LCS距离
[~, TopK_record_LCS] = Tra_distance_matrix_using_LCS_index_randomK(latLong_using_prototypes_index, FastSyncResult.X, randnum, num);

% EDR距离
[~, TopK_record_EDR] = Tra_distance_matrix_using_EDR_index_randomK(latLong_using_prototypes_index, FastSyncResult.X, randnum, num, tol);
```

## 预期输出

### 1. 可视化结果
- 原始轨迹图
- 空间点热力图  
- 轨迹网络图
- 一致性分析图

### 2. 数据文件
- `Data/str_save/` 目录下的.mat文件
- `Result/` 目录下的评估结果
- `figsVLDB/` 目录下的图像文件

### 3. 性能指标
- 轨迹相似性一致性
- 不同算法的比较结果
- NDCG@K评分

## 故障排除

### 常见错误及解决方案

1. **路径错误**
```matlab
% 确保当前工作目录正确
pwd
cd('你的项目路径')
```

2. **内存不足**
```matlab
% 减少处理的轨迹数量
param.readingNumOfTraj = 1000;  % 从100000减少到1000
```

3. **工具箱缺失**
```matlab
% 检查已安装的工具箱
ver
```

4. **数据文件缺失**
```matlab
% 检查数据目录结构
dir('Data/')
```

## 性能优化建议

### 1. 参数调优
```matlab
% 对于大数据集，增大epsilon值
param.epsilon = 0.05;  % 默认0.01

% 减少嵌入维度
param.dim = 30;  % 默认50

% 减少最近邻数量
param.Knn = 5;  % 默认10
```

### 2. 并行处理
```matlab
% 启用并行计算
parpool('local', 4);  % 使用4个核心
```

### 3. 内存管理
```matlab
% 定期清理变量
clear unnecessary_variables;
```
