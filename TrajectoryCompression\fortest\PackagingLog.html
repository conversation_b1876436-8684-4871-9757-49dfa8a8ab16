<html><meta charset="UTF-8"><pre>
<font color=blue>mcc -W 'java:Test,Class1' -T link:lib -d /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing class{Class1:/Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest.m} </font>
[Warning: Name is nonexistent or not a directory:
/Users/<USER>/Desktop/Experiment Trajectory/Dictionary_learning/cvx/builtins] 
[Warning: Name is nonexistent or not a directory:
/Users/<USER>/Desktop/Experiment Trajectory/Dictionary_learning/cvx/commands] 
[Warning: Name is nonexistent or not a directory:
/Users/<USER>/Desktop/Experiment
Trajectory/Dictionary_learning/cvx/functions] 
[Warning: Name is nonexistent or not a directory:
/Users/<USER>/Desktop/Experiment Trajectory/Dictionary_learning/cvx/lib] 
[Warning: Name is nonexistent or not a directory:
/Users/<USER>/Desktop/Experiment
Trajectory/Dictionary_learning/cvx/structures] 
[Warning: Name is nonexistent or not a directory:
/Users/<USER>/Desktop/Experiment
Trajectory/Dictionary_learning/cvx/functions/vec_] 
[Warning: Name is nonexistent or not a directory:
/Users/<USER>/Desktop/Experiment Trajectory/Dictionary_learning/cvx] 
[Warning: Adding path "/Users/<USER>/Desktop/TraCode/TrajectoryCompression"
to Compiler path instance.] 
strVersion: 1.8.0_112
Loading source files for package Test...
Constructing Javadoc information...
Standard Doclet version 1.8.0_112
Building tree for all the packages and classes...
Generating /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/doc/html/Test/Class1.html...
<font color=red>/Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/Test/Class1.java:109: warning: no @param for args</font>
<font color=red>    public static void main (String[] args)</font>
<font color=red>                       ^</font>
<font color=red>/Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/Test/Class1.java:59: warning: no @throws for com.mathworks.toolbox.javabuilder.MWException</font>
<font color=red>    public Class1() throws MWException</font>
<font color=red>           ^</font>
<font color=red>/Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/Test/Class1.java:88: warning: no @throws for com.mathworks.toolbox.javabuilder.MWException</font>
<font color=red>    public Class1(MWComponentOptions componentOptions) throws MWException</font>
<font color=red>           ^</font>
<font color=red>/Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/Test/Class1.java:77: warning: no @throws for com.mathworks.toolbox.javabuilder.MWException</font>
<font color=red>    public Class1(String pathToComponent) throws MWException</font>
<font color=red>           ^</font>
Generating /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/doc/html/Test/Class1Remote.html...
<font color=red>/Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/Test/Class1Remote.java:58: error: reference not found</font>
<font color=red>     * @throws java.jmi.RemoteException An error has occurred during the function call or </font>
<font color=red>       ^</font>
<font color=red>/Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/Test/Class1Remote.java:61: warning: no @throws for java.rmi.RemoteException</font>
<font color=red>    public Object[] fortest(int nargout, Object... rhs) throws RemoteException;</font>
<font color=red>                    ^</font>
<font color=red>/Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/Test/Class1Remote.java:64: warning: no @throws for java.rmi.RemoteException</font>
<font color=red>    void dispose() throws RemoteException;</font>
<font color=red>         ^</font>
Generating /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/doc/html/Test/TestMCRFactory.html...
Generating /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/doc/html/Test/package-frame.html...
Generating /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/doc/html/Test/package-summary.html...
<font color=red>/Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/Test/package-info.java:8: warning: empty <p> tag</font>
<font color=red> * <p></font>
<font color=red>   ^</font>
<font color=red>/Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/Test/package-info.java:15: error: unexpected end tag: </p></font>
<font color=red> * </p></font>
<font color=red>   ^</font>
<font color=red>/Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/Test/package-info.java:20: warning: empty <p> tag</font>
<font color=red> * <p></font>
<font color=red>   ^</font>
<font color=red>/Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/Test/package-info.java:26: error: unexpected end tag: </p></font>
<font color=red> * </p> </font>
<font color=red>   ^</font>
Generating /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/doc/html/Test/package-tree.html...
Generating /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/doc/html/constant-values.html...
Building index for all the packages and classes...
Generating /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/doc/html/overview-tree.html...
Generating /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/doc/html/index-all.html...
Generating /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/doc/html/deprecated-list.html...
Building index for all classes...
Generating /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/doc/html/allclasses-frame.html...
Generating /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/doc/html/allclasses-noframe.html...
Generating /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/doc/html/index.html...
Generating /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/doc/html/help-doc.html...
3 errors
8 warnings
<font color=red>Error: An error occurred while shelling out to javadoc (error code = 1).</font>
<font color=red>Unable to build executable. For more information, pass the -v option to mcc.</font>
</pre></html>
