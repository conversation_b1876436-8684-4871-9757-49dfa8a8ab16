<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_112) on Thu Jun 14 23:06:37 CST 2018 -->
<title>Index</title>
<meta name="date" content="2018-06-14">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="Test/package-summary.html">Package</a></li>
<li>Class</li>
<li><a href="Test/package-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?index-all.html" target="_top">Frames</a></li>
<li><a href="index-all.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:T">T</a>&nbsp;<a name="I:C">
<!--   -->
</a>
<h2 class="title">C</h2>
<dl>
<dt><a href="Test/Class1.html" title="class in Test"><span class="typeNameLink">Class1</span></a> - Class in <a href="Test/package-summary.html">Test</a></dt>
<dd>
<div class="block">The <code>Class1</code> class provides a Java interface to the M-functions
 from the files:</div>
</dd>
<dt><span class="memberNameLink"><a href="Test/Class1.html#Class1--">Class1()</a></span> - Constructor for class Test.<a href="Test/Class1.html" title="class in Test">Class1</a></dt>
<dd>
<div class="block">Constructs a new instance of the <code>Class1</code> class.</div>
</dd>
<dt><span class="memberNameLink"><a href="Test/Class1.html#Class1-java.lang.String-">Class1(String)</a></span> - Constructor for class Test.<a href="Test/Class1.html" title="class in Test">Class1</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Please use the constructor <a href="Test/Class1.html#Class1-com.mathworks.toolbox.javabuilder.MWComponentOptions-"><code>Class1.Class1(MWComponentOptions componentOptions)</code></a>.
 The <code>com.mathworks.toolbox.javabuilder.MWComponentOptions</code> class provides API to set the
 path to the component.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="Test/Class1.html#Class1-com.mathworks.toolbox.javabuilder.MWComponentOptions-">Class1(MWComponentOptions)</a></span> - Constructor for class Test.<a href="Test/Class1.html" title="class in Test">Class1</a></dt>
<dd>
<div class="block">Constructs a new instance of the <code>Class1</code> class.</div>
</dd>
<dt><a href="Test/Class1Remote.html" title="interface in Test"><span class="typeNameLink">Class1Remote</span></a> - Interface in <a href="Test/package-summary.html">Test</a></dt>
<dd>
<div class="block">The <code>Class1Remote</code> class provides a Java RMI-compliant interface to the 
 M-functions from the files:</div>
</dd>
</dl>
<a name="I:D">
<!--   -->
</a>
<h2 class="title">D</h2>
<dl>
<dt><span class="memberNameLink"><a href="Test/Class1.html#dispose--">dispose()</a></span> - Method in class Test.<a href="Test/Class1.html" title="class in Test">Class1</a></dt>
<dd>
<div class="block">Frees native resources associated with this object</div>
</dd>
<dt><span class="memberNameLink"><a href="Test/Class1Remote.html#dispose--">dispose()</a></span> - Method in interface Test.<a href="Test/Class1Remote.html" title="interface in Test">Class1Remote</a></dt>
<dd>
<div class="block">Frees native resources associated with the remote server object</div>
</dd>
<dt><span class="memberNameLink"><a href="Test/Class1.html#disposeAllInstances--">disposeAllInstances()</a></span> - Static method in class Test.<a href="Test/Class1.html" title="class in Test">Class1</a></dt>
<dd>
<div class="block">Calls dispose method for each outstanding instance of this class.</div>
</dd>
</dl>
<a name="I:F">
<!--   -->
</a>
<h2 class="title">F</h2>
<dl>
<dt><span class="memberNameLink"><a href="Test/Class1.html#fortest-java.util.List-java.util.List-">fortest(List, List)</a></span> - Method in class Test.<a href="Test/Class1.html" title="class in Test">Class1</a></dt>
<dd>
<div class="block">Provides the interface for calling the <code>fortest</code> M-function 
 where the first input, an instance of List, receives the output of the M-function and
 the second input, also an instance of List, provides the input to the M-function.</div>
</dd>
<dt><span class="memberNameLink"><a href="Test/Class1.html#fortest-java.lang.Object:A-java.lang.Object:A-">fortest(Object[], Object[])</a></span> - Method in class Test.<a href="Test/Class1.html" title="class in Test">Class1</a></dt>
<dd>
<div class="block">Provides the interface for calling the <code>fortest</code> M-function 
 where the first input, an Object array, receives the output of the M-function and
 the second input, also an Object array, provides the input to the M-function.</div>
</dd>
<dt><span class="memberNameLink"><a href="Test/Class1.html#fortest-int-java.lang.Object...-">fortest(int, Object...)</a></span> - Method in class Test.<a href="Test/Class1.html" title="class in Test">Class1</a></dt>
<dd>
<div class="block">Provides the standard interface for calling the <code>fortest</code>
 M-function with 2 input arguments.</div>
</dd>
<dt><span class="memberNameLink"><a href="Test/Class1Remote.html#fortest-int-java.lang.Object...-">fortest(int, Object...)</a></span> - Method in interface Test.<a href="Test/Class1Remote.html" title="interface in Test">Class1Remote</a></dt>
<dd>
<div class="block">Provides the standard interface for calling the <code>fortest</code> M-function 
 with 2 input arguments.</div>
</dd>
</dl>
<a name="I:M">
<!--   -->
</a>
<h2 class="title">M</h2>
<dl>
<dt><span class="memberNameLink"><a href="Test/Class1.html#main-java.lang.String:A-">main(String[])</a></span> - Static method in class Test.<a href="Test/Class1.html" title="class in Test">Class1</a></dt>
<dd>
<div class="block">Invokes the first m-function specified by MCC, with any arguments given on
 the command line, and prints the result.</div>
</dd>
</dl>
<a name="I:N">
<!--   -->
</a>
<h2 class="title">N</h2>
<dl>
<dt><span class="memberNameLink"><a href="Test/TestMCRFactory.html#newInstance-com.mathworks.toolbox.javabuilder.MWComponentOptions-">newInstance(MWComponentOptions)</a></span> - Static method in class Test.<a href="Test/TestMCRFactory.html" title="class in Test">TestMCRFactory</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="Test/TestMCRFactory.html#newInstance--">newInstance()</a></span> - Static method in class Test.<a href="Test/TestMCRFactory.html" title="class in Test">TestMCRFactory</a></dt>
<dd>&nbsp;</dd>
</dl>
<a name="I:T">
<!--   -->
</a>
<h2 class="title">T</h2>
<dl>
<dt><a href="Test/package-summary.html">Test</a> - package Test</dt>
<dd>
<div class="block">This package was created using MATLAB Java Package.</div>
</dd>
<dt><a href="Test/TestMCRFactory.html" title="class in Test"><span class="typeNameLink">TestMCRFactory</span></a> - Class in <a href="Test/package-summary.html">Test</a></dt>
<dd>
<div class="block"><i>INTERNAL USE ONLY</i></div>
</dd>
</dl>
<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:T">T</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="Test/package-summary.html">Package</a></li>
<li>Class</li>
<li><a href="Test/package-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?index-all.html" target="_top">Frames</a></li>
<li><a href="index-all.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
