%% 测试Octave兼容性的简化版本
% 这个脚本测试原项目的核心功能是否能在Octave中运行

fprintf('=== 语义轨迹挖掘项目 - Octave兼容性测试 ===\n');

%% 1. 基础环境测试
fprintf('\n1. 测试基础环境...\n');
try
    % 测试基本数学运算
    test_matrix = rand(100, 3);
    test_result = mean(test_matrix);
    fprintf('   ✓ 基础矩阵运算正常\n');
    
    % 测试绘图功能
    figure('visible', 'off');
    plot(1:10, rand(1,10));
    close;
    fprintf('   ✓ 绘图功能正常\n');
    
catch ME
    fprintf('   ✗ 基础环境测试失败: %s\n', ME.message);
    return;
end

%% 2. 生成合成轨迹数据
fprintf('\n2. 生成合成轨迹数据...\n');
try
    % 参数设置
    boundary = 100;
    numTrajectories = 10;
    
    % 生成轨迹数据
    latLong = [];
    for i = 1:numTrajectories
        % 生成随机起点和终点
        startPoint = rand(1,2) * (boundary-1) + 0.5;
        endPoint = rand(1,2) * (boundary-1) + 0.5;
        
        % 生成路径点
        numPoints = randi([10, 20]);
        t = linspace(0, 1, numPoints);
        path = [startPoint(1) + t' * (endPoint(1) - startPoint(1)), ...
                startPoint(2) + t' * (endPoint(2) - startPoint(2))];
        
        % 添加噪声
        noise = randn(size(path)) * 0.5;
        path = path + noise;
        
        % 确保在边界内
        path = max(0.5, min(boundary-0.5, path));
        
        % 添加轨迹ID
        trajData = [i * ones(size(path,1), 1), path];
        latLong = [latLong; trajData];
    end
    
    fprintf('   ✓ 生成了 %d 条轨迹，共 %d 个GPS点\n', numTrajectories, size(latLong,1));
    
catch ME
    fprintf('   ✗ 数据生成失败: %s\n', ME.message);
    return;
end

%% 3. 测试数据标准化
fprintf('\n3. 测试数据标准化...\n');
try
    % 提取坐标数据
    X = latLong(:, [2,3]);
    
    % 标准化到 [0, pi/2] 范围
    axisRange = [0.5, boundary-0.5, 0.5, boundary-0.5];
    X_norm = zeros(size(X));
    X_norm(:,1) = (X(:,1) - axisRange(3)) / (axisRange(4) - axisRange(3)) * pi/2;
    X_norm(:,2) = (X(:,2) - axisRange(1)) / (axisRange(2) - axisRange(1)) * pi/2;
    
    fprintf('   ✓ 数据标准化完成\n');
    fprintf('   原始数据范围: [%.2f, %.2f] x [%.2f, %.2f]\n', ...
            min(X(:,1)), max(X(:,1)), min(X(:,2)), max(X(:,2)));
    fprintf('   标准化后范围: [%.2f, %.2f] x [%.2f, %.2f]\n', ...
            min(X_norm(:,1)), max(X_norm(:,1)), min(X_norm(:,2)), max(X_norm(:,2)));
    
catch ME
    fprintf('   ✗ 数据标准化失败: %s\n', ME.message);
    return;
end

%% 4. 测试简化聚类
fprintf('\n4. 测试简化聚类算法...\n');
try
    % 简化的聚类参数
    epsilon = 0.1;
    minPts = 3;
    
    % 计算距离矩阵
    numPoints = size(X_norm, 1);
    distances = zeros(numPoints, numPoints);
    for i = 1:numPoints
        for j = 1:numPoints
            distances(i,j) = sqrt(sum((X_norm(i,:) - X_norm(j,:)).^2));
        end
    end
    
    % 简化的DBSCAN聚类
    visited = false(numPoints, 1);
    clustered = false(numPoints, 1);
    clusterLabels = zeros(numPoints, 1);
    clusterID = 0;
    
    for i = 1:numPoints
        if visited(i)
            continue;
        end
        visited(i) = true;
        
        % 找邻居
        neighbors = find(distances(i,:) <= epsilon);
        
        if length(neighbors) < minPts
            % 标记为噪声
            clusterLabels(i) = -1;
        else
            % 开始新聚类
            clusterID = clusterID + 1;
            clusterLabels(i) = clusterID;
            clustered(i) = true;
            
            % 扩展聚类
            j = 1;
            while j <= length(neighbors)
                neighbor = neighbors(j);
                if ~visited(neighbor)
                    visited(neighbor) = true;
                    newNeighbors = find(distances(neighbor,:) <= epsilon);
                    if length(newNeighbors) >= minPts
                        neighbors = [neighbors, newNeighbors];
                    end
                end
                
                if ~clustered(neighbor)
                    clusterLabels(neighbor) = clusterID;
                    clustered(neighbor) = true;
                end
                j = j + 1;
            end
        end
    end
    
    % 计算聚类结果
    uniqueClusters = unique(clusterLabels(clusterLabels > 0));
    numClusters = length(uniqueClusters);
    
    fprintf('   ✓ 聚类完成，发现 %d 个聚类\n', numClusters);
    
    % 计算原型点
    prototypes = zeros(numClusters, 2);
    weights = zeros(numClusters, 1);
    
    for i = 1:numClusters
        clusterPoints = X_norm(clusterLabels == uniqueClusters(i), :);
        prototypes(i, :) = mean(clusterPoints, 1);
        weights(i) = size(clusterPoints, 1);
    end
    
    fprintf('   原型点数量: %d\n', size(prototypes, 1));
    
catch ME
    fprintf('   ✗ 聚类算法失败: %s\n', ME.message);
    return;
end

%% 5. 测试可视化
fprintf('\n5. 测试可视化功能...\n');
try
    % 创建图形
    figure('Position', [100, 100, 800, 600]);
    
    % 子图1: 原始轨迹
    subplot(2,2,1);
    uniqueTrajs = unique(latLong(:,1));
    colors = lines(length(uniqueTrajs));
    
    for i = 1:length(uniqueTrajs)
        trajPoints = latLong(latLong(:,1) == uniqueTrajs(i), :);
        plot(trajPoints(:,3), trajPoints(:,2), 'Color', colors(i,:), 'LineWidth', 1.5);
        hold on;
    end
    title('原始轨迹');
    xlabel('经度');
    ylabel('纬度');
    grid on;
    
    % 子图2: 标准化数据
    subplot(2,2,2);
    scatter(X_norm(:,2), X_norm(:,1), 20, latLong(:,1), 'filled');
    title('标准化数据');
    xlabel('标准化经度');
    ylabel('标准化纬度');
    colorbar;
    grid on;
    
    % 子图3: 聚类结果
    subplot(2,2,3);
    if numClusters > 0
        scatter(X_norm(:,2), X_norm(:,1), 30, clusterLabels, 'filled');
        hold on;
        scatter(prototypes(:,2), prototypes(:,1), 100, 'r', 'x', 'LineWidth', 3);
        title('聚类结果');
        xlabel('标准化经度');
        ylabel('标准化纬度');
        colorbar;
        grid on;
    end
    
    % 子图4: 原型点权重
    subplot(2,2,4);
    if numClusters > 0
        scatter(prototypes(:,2), prototypes(:,1), weights*10, weights, 'filled');
        title('原型点权重');
        xlabel('标准化经度');
        ylabel('标准化纬度');
        colorbar;
        grid on;
    end
    
    % 保存图形
    saveas(gcf, 'octave_test_results.png');
    fprintf('   ✓ 可视化完成，结果保存为 octave_test_results.png\n');
    
catch ME
    fprintf('   ✗ 可视化失败: %s\n', ME.message);
end

%% 6. 总结
fprintf('\n=== 测试完成 ===\n');
fprintf('如果所有测试都通过，说明Octave可以运行项目的基础功能。\n');
fprintf('要运行完整的原项目，请参考以下文件:\n');
fprintf('- MATLAB_SETUP_GUIDE.md: 完整MATLAB环境设置\n');
fprintf('- PROJECT_ANALYSIS.md: 项目功能对比分析\n');
fprintf('\n原项目的核心文件:\n');
fprintf('- SyntheticExperiment.m: 合成数据实验\n');
fprintf('- expriment_realworld_data.m: 真实数据实验\n');
fprintf('- Fast_Hierarchical_Synchronization.m: 核心算法\n');
