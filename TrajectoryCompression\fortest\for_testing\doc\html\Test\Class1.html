<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_112) on Thu Jun 14 23:06:37 CST 2018 -->
<title>Class1</title>
<meta name="date" content="2018-06-14">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Class1";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":10,"i3":10,"i4":10,"i5":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../Test/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../Test/Class1Remote.html" title="interface in Test"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?Test/Class1.html" target="_top">Frames</a></li>
<li><a href="Class1.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.mathworks.toolbox.javabuilder.internal.MWComponentInstance">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">Test</div>
<h2 title="Class Class1" class="title">Class Class1</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.mathworks.toolbox.javabuilder.internal.MWComponentInstance&lt;<a href="../Test/Class1.html" title="class in Test">Class1</a>&gt;</li>
<li>
<ul class="inheritance">
<li>Test.Class1</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>com.mathworks.toolbox.javabuilder.Disposable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Class1</span>
extends com.mathworks.toolbox.javabuilder.internal.MWComponentInstance&lt;<a href="../Test/Class1.html" title="class in Test">Class1</a>&gt;</pre>
<div class="block">The <code>Class1</code> class provides a Java interface to the M-functions
 from the files:
 <pre>
  /Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest.m
 </pre>
 The <a href="../Test/Class1.html#dispose--"><code>dispose()</code></a> method <b>must</b> be called on a <code>Class1</code> instance 
 when it is no longer needed to ensure that native resources allocated by this class 
 are properly freed.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.mathworks.toolbox.javabuilder.internal.MWComponentInstance">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.mathworks.toolbox.javabuilder.internal.MWComponentInstance</h3>
<code>fMCR</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../Test/Class1.html#Class1--">Class1</a></span>()</code>
<div class="block">Constructs a new instance of the <code>Class1</code> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../Test/Class1.html#Class1-com.mathworks.toolbox.javabuilder.MWComponentOptions-">Class1</a></span>(com.mathworks.toolbox.javabuilder.MWComponentOptions&nbsp;componentOptions)</code>
<div class="block">Constructs a new instance of the <code>Class1</code> class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../Test/Class1.html#Class1-java.lang.String-">Class1</a></span>(java.lang.String&nbsp;pathToComponent)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Please use the constructor <a href="../Test/Class1.html#Class1-com.mathworks.toolbox.javabuilder.MWComponentOptions-"><code>Class1(MWComponentOptions componentOptions)</code></a>.
 The <code>com.mathworks.toolbox.javabuilder.MWComponentOptions</code> class provides API to set the
 path to the component.</span></div>
</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../Test/Class1.html#dispose--">dispose</a></span>()</code>
<div class="block">Frees native resources associated with this object</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../Test/Class1.html#disposeAllInstances--">disposeAllInstances</a></span>()</code>
<div class="block">Calls dispose method for each outstanding instance of this class.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.Object[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../Test/Class1.html#fortest-int-java.lang.Object...-">fortest</a></span>(int&nbsp;nargout,
       java.lang.Object...&nbsp;rhs)</code>
<div class="block">Provides the standard interface for calling the <code>fortest</code>
 M-function with 2 input arguments.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../Test/Class1.html#fortest-java.util.List-java.util.List-">fortest</a></span>(java.util.List&nbsp;lhs,
       java.util.List&nbsp;rhs)</code>
<div class="block">Provides the interface for calling the <code>fortest</code> M-function 
 where the first input, an instance of List, receives the output of the M-function and
 the second input, also an instance of List, provides the input to the M-function.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../Test/Class1.html#fortest-java.lang.Object:A-java.lang.Object:A-">fortest</a></span>(java.lang.Object[]&nbsp;lhs,
       java.lang.Object[]&nbsp;rhs)</code>
<div class="block">Provides the interface for calling the <code>fortest</code> M-function 
 where the first input, an Object array, receives the output of the M-function and
 the second input, also an Object array, provides the input to the M-function.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../Test/Class1.html#main-java.lang.String:A-">main</a></span>(java.lang.String[]&nbsp;args)</code>
<div class="block">Invokes the first m-function specified by MCC, with any arguments given on
 the command line, and prints the result.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.mathworks.toolbox.javabuilder.internal.MWComponentInstance">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.mathworks.toolbox.javabuilder.internal.MWComponentInstance</h3>
<code>finalize, waitForFigures</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Class1--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Class1</h4>
<pre>public&nbsp;Class1()
       throws com.mathworks.toolbox.javabuilder.MWException</pre>
<div class="block">Constructs a new instance of the <code>Class1</code> class.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mathworks.toolbox.javabuilder.MWException</code></dd>
</dl>
</li>
</ul>
<a name="Class1-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Class1</h4>
<pre>public&nbsp;Class1(java.lang.String&nbsp;pathToComponent)
       throws com.mathworks.toolbox.javabuilder.MWException</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Please use the constructor <a href="../Test/Class1.html#Class1-com.mathworks.toolbox.javabuilder.MWComponentOptions-"><code>Class1(MWComponentOptions componentOptions)</code></a>.
 The <code>com.mathworks.toolbox.javabuilder.MWComponentOptions</code> class provides API to set the
 path to the component.</span></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>pathToComponent</code> - Path to component directory.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mathworks.toolbox.javabuilder.MWException</code></dd>
</dl>
</li>
</ul>
<a name="Class1-com.mathworks.toolbox.javabuilder.MWComponentOptions-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Class1</h4>
<pre>public&nbsp;Class1(com.mathworks.toolbox.javabuilder.MWComponentOptions&nbsp;componentOptions)
       throws com.mathworks.toolbox.javabuilder.MWException</pre>
<div class="block">Constructs a new instance of the <code>Class1</code> class. Use this constructor 
 to specify the options required to instantiate this component.  The options will 
 be specific to the instance of this component being created.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>componentOptions</code> - Options specific to the component.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mathworks.toolbox.javabuilder.MWException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="dispose--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dispose</h4>
<pre>public&nbsp;void&nbsp;dispose()</pre>
<div class="block">Frees native resources associated with this object</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>dispose</code>&nbsp;in interface&nbsp;<code>com.mathworks.toolbox.javabuilder.Disposable</code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>dispose</code>&nbsp;in class&nbsp;<code>com.mathworks.toolbox.javabuilder.internal.MWComponentInstance&lt;<a href="../Test/Class1.html" title="class in Test">Class1</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="main-java.lang.String:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>main</h4>
<pre>public static&nbsp;void&nbsp;main(java.lang.String[]&nbsp;args)</pre>
<div class="block">Invokes the first m-function specified by MCC, with any arguments given on
 the command line, and prints the result.</div>
</li>
</ul>
<a name="disposeAllInstances--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>disposeAllInstances</h4>
<pre>public static&nbsp;void&nbsp;disposeAllInstances()</pre>
<div class="block">Calls dispose method for each outstanding instance of this class.</div>
</li>
</ul>
<a name="fortest-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fortest</h4>
<pre>public&nbsp;void&nbsp;fortest(java.util.List&nbsp;lhs,
                    java.util.List&nbsp;rhs)
             throws com.mathworks.toolbox.javabuilder.MWException</pre>
<div class="block">Provides the interface for calling the <code>fortest</code> M-function 
 where the first input, an instance of List, receives the output of the M-function and
 the second input, also an instance of List, provides the input to the M-function.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>lhs</code> - List in which to return outputs. Number of outputs (nargout) is
 determined by allocated size of this List. Outputs are returned as
 sub-classes of <code>com.mathworks.toolbox.javabuilder.MWArray</code>.
 Each output array should be freed by calling its <code>dispose()</code>
 method.</dd>
<dd><code>rhs</code> - List containing inputs. Number of inputs (nargin) is determined
 by the allocated size of this List. Input arguments may be passed as
 sub-classes of <code>com.mathworks.toolbox.javabuilder.MWArray</code>, or
 as arrays of any supported Java type. Arguments passed as Java types are
 converted to MATLAB arrays according to default conversion rules.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mathworks.toolbox.javabuilder.MWException</code> - An error has occurred during the function call.</dd>
</dl>
</li>
</ul>
<a name="fortest-java.lang.Object:A-java.lang.Object:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fortest</h4>
<pre>public&nbsp;void&nbsp;fortest(java.lang.Object[]&nbsp;lhs,
                    java.lang.Object[]&nbsp;rhs)
             throws com.mathworks.toolbox.javabuilder.MWException</pre>
<div class="block">Provides the interface for calling the <code>fortest</code> M-function 
 where the first input, an Object array, receives the output of the M-function and
 the second input, also an Object array, provides the input to the M-function.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>lhs</code> - array in which to return outputs. Number of outputs (nargout)
 is determined by allocated size of this array. Outputs are returned as
 sub-classes of <code>com.mathworks.toolbox.javabuilder.MWArray</code>.
 Each output array should be freed by calling its <code>dispose()</code>
 method.</dd>
<dd><code>rhs</code> - array containing inputs. Number of inputs (nargin) is
 determined by the allocated size of this array. Input arguments may be
 passed as sub-classes of
 <code>com.mathworks.toolbox.javabuilder.MWArray</code>, or as arrays of
 any supported Java type. Arguments passed as Java types are converted to
 MATLAB arrays according to default conversion rules.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mathworks.toolbox.javabuilder.MWException</code> - An error has occurred during the function call.</dd>
</dl>
</li>
</ul>
<a name="fortest-int-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>fortest</h4>
<pre>public&nbsp;java.lang.Object[]&nbsp;fortest(int&nbsp;nargout,
                                  java.lang.Object...&nbsp;rhs)
                           throws com.mathworks.toolbox.javabuilder.MWException</pre>
<div class="block">Provides the standard interface for calling the <code>fortest</code>
 M-function with 2 input arguments.
 Input arguments may be passed as sub-classes of
 <code>com.mathworks.toolbox.javabuilder.MWArray</code>, or as arrays of
 any supported Java type. Arguments passed as Java types are converted to
 MATLAB arrays according to default conversion rules.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nargout</code> - Number of outputs to return.</dd>
<dd><code>rhs</code> - The inputs to the M function.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Array of length nargout containing the function outputs. Outputs
 are returned as sub-classes of
 <code>com.mathworks.toolbox.javabuilder.MWArray</code>. Each output array
 should be freed by calling its <code>dispose()</code> method.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mathworks.toolbox.javabuilder.MWException</code> - An error has occurred during the function call.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../Test/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../Test/Class1Remote.html" title="interface in Test"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?Test/Class1.html" target="_top">Frames</a></li>
<li><a href="Class1.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.mathworks.toolbox.javabuilder.internal.MWComponentInstance">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
