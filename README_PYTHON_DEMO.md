# Semantic Trajectory Mining - Python Demo

这是一个语义轨迹挖掘项目的Python演示版本，基于原始的MATLAB项目实现。

## 项目概述

语义轨迹挖掘是一个用于分析和理解移动轨迹数据的框架。该项目的主要目标是：

1. **轨迹数据预处理**: 清理和标准化GPS轨迹数据
2. **空间点聚类**: 使用快速分层同步算法识别重要的空间点（SPs）
3. **轨迹表示**: 将原始轨迹转换为基于语义点的表示
4. **轨迹嵌入**: 创建轨迹的向量表示用于相似性分析
5. **可视化**: 提供多种可视化方法来理解数据和结果

## 原始MATLAB项目结构

原始项目包含以下主要组件：

### 核心算法文件
- `Fast_Hierarchical_Synchronization.m` - 快速分层同步聚类算法
- `AsymmetricSPEmbedding.m` - 非对称空间点嵌入
- `TrajectoryEmbedding.m` - 轨迹嵌入算法
- `matching_to_SPs.m` - 轨迹到空间点的匹配

### 数据读取模块
- `GeoLife_trajectory_reading.m` - GeoLife数据集读取
- `TDrive_trajectory_reading.m` - T-Drive数据集读取
- `kaggle_trajectory_reading.m` - Kaggle轨迹数据读取
- `Hurricane_trajectory_reading.m` - 飓风轨迹数据读取

### 实验和可视化
- `expriment_realworld_data.m` - 真实数据实验
- `SyntheticExperiment.m` - 合成数据实验
- `visualization.m` - 可视化工具

### 轨迹压缩模块
- `TrajectoryCompression/` - 轨迹压缩相关算法

## Python演示版本

由于原始项目需要MATLAB环境，我们创建了一个Python版本的简化实现来演示核心概念。

### 环境要求

```bash
# Python 3.7+
pip install numpy matplotlib pandas scipy scikit-learn
```

### 运行演示

```bash
python semantic_trajectory_mining_demo.py
```

### 演示功能

1. **合成数据生成**: 创建模拟的轨迹数据
2. **轨迹可视化**: 显示原始轨迹
3. **空间聚类**: 使用DBSCAN进行空间点聚类
4. **原型点可视化**: 显示聚类得到的原型点
5. **轨迹嵌入**: 创建轨迹的向量表示
6. **嵌入可视化**: 使用PCA降维显示轨迹嵌入

## 运行原始MATLAB项目

要运行原始的MATLAB项目，需要：

### 1. 环境准备

```matlab
% 确保MATLAB已安装以下工具箱：
% - Statistics and Machine Learning Toolbox
% - Image Processing Toolbox
% - Robotics System Toolbox (用于路径规划)
```

### 2. 数据准备

项目支持多种数据集：

- **GeoLife**: 微软研究院的GPS轨迹数据集
- **T-Drive**: 北京出租车轨迹数据集
- **Kaggle**: 葡萄牙出租车轨迹数据集
- **Hurricane**: 飓风轨迹数据

### 3. 运行步骤

#### 方法1: 合成数据实验
```matlab
% 运行合成数据实验
SyntheticExperiment;
```

#### 方法2: 真实数据实验
```matlab
% 编辑 expriment_realworld_data.m 选择数据集
% 取消注释相应的数据集部分，例如：

% 对于GeoLife数据集：
param.inputFile = 'Data/GeoLife';
param.readingNumOfTraj = 100000;
param.imgMap = 'Data/beijing1.png';
param.axisRange = [116.1935 116.5531 39.7513 40.0334];

% 然后运行：
expriment_realworld_data;
```

### 4. 主要参数配置

```matlab
% 聚类参数
param.Knn = 10;              % 每个点的最近邻数量
param.epsilon = 0.01;        % 交互距离范围
param.maxLayer = 20;         % 最大层数

% 嵌入参数
param.dim = 50;              % 嵌入维度
param.learningRate = 0.025;  % 学习率
param.window = 2;            % 窗口大小
```

## 项目输出

### 可视化结果
- 原始轨迹图
- 空间点热力图
- 轨迹网络图
- 嵌入空间可视化

### 数据文件
- `.mat` 文件保存处理结果
- 图像文件保存可视化结果

### 评估指标
- 轨迹相似性一致性
- NDCG@K 评分
- 聚类质量指标

## 故障排除

### 常见问题

1. **内存不足**
   - 减少 `param.readingNumOfTraj` 参数
   - 使用更小的数据集

2. **路径问题**
   - 确保数据文件路径正确
   - 检查 `Data/` 目录结构

3. **工具箱缺失**
   - 安装必要的MATLAB工具箱
   - 或使用Octave作为替代

### 性能优化

- 调整聚类参数以平衡精度和速度
- 使用并行计算工具箱加速处理
- 预处理数据以减少计算量

## 扩展功能

项目还包含以下高级功能：

- **轨迹压缩**: 减少轨迹数据存储空间
- **查询处理**: 基于位置的轨迹查询
- **多数据集比较**: 不同算法的性能比较
- **实时处理**: 在线轨迹处理能力

## 参考文献

该项目基于以下研究工作：
- 快速分层同步算法
- 非对称空间点嵌入
- 轨迹语义表示学习

## 联系信息

如有问题或建议，请参考项目中的 `User Guide-1.3.pdf` 文档。
