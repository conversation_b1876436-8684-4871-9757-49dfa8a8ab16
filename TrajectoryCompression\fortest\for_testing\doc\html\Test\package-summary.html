<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_112) on Thu Jun 14 23:06:37 CST 2018 -->
<title>Test</title>
<meta name="date" content="2018-06-14">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Test";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../Test/package-summary.html">Package</a></li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Package</li>
<li>Next&nbsp;Package</li>
</ul>
<ul class="navList">
<li><a href="../index.html?Test/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;Test</h1>
<div class="docSummary">
<div class="block">This package was created using MATLAB Java Package.</div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../Test/Class1Remote.html" title="interface in Test">Class1Remote</a></td>
<td class="colLast">
<div class="block">The <code>Class1Remote</code> class provides a Java RMI-compliant interface to the 
 M-functions from the files:</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../Test/Class1.html" title="class in Test">Class1</a></td>
<td class="colLast">
<div class="block">The <code>Class1</code> class provides a Java interface to the M-functions
 from the files:</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../Test/TestMCRFactory.html" title="class in Test">TestMCRFactory</a></td>
<td class="colLast">
<div class="block"><i>INTERNAL USE ONLY</i></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package Test Description">Package Test Description</h2>
<div class="block"><p>This package was created using MATLAB Java Package. The classes included in this 
 package are wrappers around MATLAB functions which were used when compiling this 
 package in MATLAB. These classes have public methods that provide access to the 
 MATLAB functions used by MATLAB Java Package during compilation.</p>
 <h3><b>IMPORTANT : </b>What you need to use this package successfully :</h3>
 <h3>MATLAB Runtime</h3>
 <p>
 <ul>
 <li>MATLAB Runtime is the collection of platform specific native libraries required to execute M functions exported by the classes of this package</li>
 <li>It can be made available either by installing MATLAB, MATLAB Compiler and MATLAB Java Package or by just running MCRInstaller executable</li>
 <li>This package is compatible with MATLAB Runtime version 9.1 only.</li>
 <li>Please contact the creator of this package for specific details about the MATLAB Runtime (e.g what version of MATLAB it originated with since MATLAB Runtime version is tied to the version of MATLAB)</li>
 </ul> 
 </p>
 <p><b>NOTE: </b>Creating the first instance of one of the classes from this package is more time
 consuming than creating subsequent instances since the native libraries from the MATLAB Runtime
 must be loaded.</p>
 <h3>javabuilder.jar</h3> 
 <p>
 <ul>
 <li>Provides classes that act as the bridge between your application and the MATLAB Runtime</li>
 <li>Located in the $MCR/toolbox/javabuilder/jar directory (where $MCR is the root of an installation of either MATLAB or MCRInstaller)</li>
 <li>The <code>Test</code> package will only work with the javabuilder.jar file included with MATLAB Runtime version 9.1</li>
 </ul>
 </p> 
 <p><b>NOTE: </b><code>com.mathworks.toolbox.javabuilder.MWArray</code> is one of many data 
 conversion classes provided in javabuilder.jar. MWArray is an abstract class representing a 
 MATLAB array. Each MATLAB array type has a corresponding concrete class type in the 
 MWArray class hierarchy. The public methods that represent MATLAB functions, for the classes 
 in the <code>Test</code> package, can take instances of these concrete classes as 
 input. These methods can also take native Java primitive or array types as input. These native 
 types are converted to appropriate MWArray types using data conversion rules provided by 
 MATLAB Java Package. For instance, a Java primitive double is converted into an instance
 of MWNumericArray (a subclass of MWArray).</p></div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../Test/package-summary.html">Package</a></li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Package</li>
<li>Next&nbsp;Package</li>
</ul>
<ul class="navList">
<li><a href="../index.html?Test/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
