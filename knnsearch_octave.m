function [IDX, D] = knnsearch_octave(X, Y, varargin)
% KNNSEARCH_OCTAVE - Octave兼容的k近邻搜索函数
% 这是MATLAB knnsearch函数的简化版本
%
% 输入:
%   X - 参考点集 (n x d 矩阵)
%   Y - 查询点集 (m x d 矩阵)
%   varargin - 可选参数，支持 'K', k_value
%
% 输出:
%   IDX - 最近邻索引 (m x k 矩阵)
%   D - 距离 (m x k 矩阵)

% 解析输入参数
k = 1; % 默认值
for i = 1:2:length(varargin)
    if strcmpi(varargin{i}, 'K')
        k = varargin{i+1};
    end
end

% 获取数据维度
[n, d] = size(X);
[m, ~] = size(Y);

% 初始化输出
IDX = zeros(m, k);
D = zeros(m, k);

% 对每个查询点找k个最近邻
for i = 1:m
    % 计算当前查询点到所有参考点的距离
    distances = zeros(n, 1);
    for j = 1:n
        distances(j) = sqrt(sum((Y(i,:) - X(j,:)).^2));
    end
    
    % 找到k个最小距离及其索引
    [sorted_distances, sorted_indices] = sort(distances);
    
    % 存储结果
    IDX(i, :) = sorted_indices(1:k);
    D(i, :) = sorted_distances(1:k);
end

end
