<deployment-project plugin="plugin.ezdeploy" plugin-version="1.0">
  <configuration file="/Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest.prj" location="/Users/<USER>/Desktop/TraCode/TrajectoryCompression" name="fortest" target="target.ezdeploy.library" target-name="Library Compiler">
    <param.appname>fortest</param.appname>
    <param.icon />
    <param.icons />
    <param.version>1.0</param.version>
    <param.authnamewatermark />
    <param.email />
    <param.company />
    <param.summary />
    <param.description />
    <param.screenshot />
    <param.guid />
    <param.installpath.string>/fortest/</param.installpath.string>
    <param.installpath.combo>option.installpath.systemwideapp</param.installpath.combo>
    <param.logo />
    <param.install.notes>In the following directions, replace MCR_ROOT by the directory where the MATLAB Runtime is installed on the target machine.

If the environment variable DYLD_LIBRARY_PATH is undefined, set it to the concatenation of the following strings:

    MCR_ROOT/v91/runtime/maci64:
    MCR_ROOT/v91/sys/os/maci64:
    MCR_ROOT/v91/bin/maci64

If it is defined, set it to the concatenation of these strings:

    ${LD_LIBRARY_PATH}: 
    MCR_ROOT/v91/runtime/maci64:
    MCR_ROOT/v91/sys/os/maci64:
    MCR_ROOT/v91/bin/maci64</param.install.notes>
    <param.target.install.notes />
    <param.intermediate>${PROJECT_ROOT}/fortest/for_testing</param.intermediate>
    <param.files.only>${PROJECT_ROOT}/fortest/for_redistribution_files_only</param.files.only>
    <param.output>${PROJECT_ROOT}/fortest/for_redistribution</param.output>
    <param.logdir>${PROJECT_ROOT}/fortest</param.logdir>
    <param.enable.clean.build>false</param.enable.clean.build>
    <param.user.defined.mcr.options />
    <param.embed.ctf>true</param.embed.ctf>
    <param.target.type>subtarget.java.package</param.target.type>
    <param.support.packages />
    <param.namespace>Test</param.namespace>
    <param.classorg />
    <param.web.mcr>true</param.web.mcr>
    <param.package.mcr>false</param.package.mcr>
    <param.no.mcr>false</param.no.mcr>
    <param.web.mcr.name>MyAppInstaller_web</param.web.mcr.name>
    <param.package.mcr.name>MyAppInstaller_mcr</param.package.mcr.name>
    <param.no.mcr.name>MyAppInstaller_app</param.no.mcr.name>
    <param.windows.command.prompt>false</param.windows.command.prompt>
    <param.create.log>false</param.create.log>
    <param.log.file />
    <param.user.only.registration>false</param.user.only.registration>
    <param.net.framework>option.net.framework.default</param.net.framework>
    <param.assembly.type>false</param.assembly.type>
    <param.encryption.key.file />
    <param.net.enable.remoting>false</param.net.enable.remoting>
    <param.net.tsa.enable>false</param.net.tsa.enable>
    <param.net.tsa.assembly />
    <param.net.tsa.interface />
    <param.net.tsa.namespace>Test</param.net.tsa.namespace>
    <param.net.tsa.superclass>Class1</param.net.tsa.superclass>
    <param.net.tsa.metadata />
    <param.net.tsa.metadata.assembly>/Users/<USER>/Documents/MATLAB</param.net.tsa.metadata.assembly>
    <param.net.saved.interface />
    <unset>
      <param.icon />
      <param.icons />
      <param.version />
      <param.authnamewatermark />
      <param.email />
      <param.company />
      <param.summary />
      <param.description />
      <param.screenshot />
      <param.guid />
      <param.installpath.string />
      <param.installpath.combo />
      <param.logo />
      <param.target.install.notes />
      <param.intermediate />
      <param.files.only />
      <param.output />
      <param.logdir />
      <param.enable.clean.build />
      <param.user.defined.mcr.options />
      <param.embed.ctf />
      <param.support.packages />
      <param.classorg />
      <param.web.mcr />
      <param.package.mcr />
      <param.no.mcr />
      <param.web.mcr.name />
      <param.package.mcr.name />
      <param.no.mcr.name />
      <param.windows.command.prompt />
      <param.create.log />
      <param.log.file />
      <param.user.only.registration />
      <param.net.framework />
      <param.assembly.type />
      <param.encryption.key.file />
      <param.net.enable.remoting />
      <param.net.tsa.enable />
      <param.net.tsa.assembly />
      <param.net.tsa.interface />
      <param.net.tsa.namespace />
      <param.net.tsa.superclass />
      <param.net.tsa.metadata />
      <param.net.tsa.metadata.assembly />
      <param.net.saved.interface />
    </unset>
    <fileset.exports>
      <file>${PROJECT_ROOT}/fortest.m</file>
    </fileset.exports>
    <fileset.classes>
      <entity.package name="">
        <entity.class name="Class1">
          <file>${PROJECT_ROOT}/fortest.m</file>
        </entity.class>
      </entity.package>
    </fileset.classes>
    <fileset.resources />
    <fileset.depfun />
    <fileset.package />
    <build-deliverables>
      <file location="${PROJECT_ROOT}/fortest/for_testing" name="doc" optional="false">/Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/doc</file>
      <file location="${PROJECT_ROOT}/fortest/for_testing" name="Test.jar" optional="false">/Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/Test.jar</file>
      <file location="${PROJECT_ROOT}/fortest/for_testing" name="readme.txt" optional="true">/Users/<USER>/Desktop/TraCode/TrajectoryCompression/fortest/for_testing/readme.txt</file>
    </build-deliverables>
    <workflow />
    <matlab>
      <root>/Applications/MATLAB_R2016b.app</root>
      <toolboxes>
        <toolbox name="fixedpoint" />
        <toolbox name="matlabcoder" />
        <toolbox name="matlabhdlcoder" />
        <toolbox name="embeddedcoder" />
      </toolboxes>
      <toolbox>
        <fixedpoint>
          <enabled>true</enabled>
        </fixedpoint>
      </toolbox>
      <toolbox>
        <matlabcoder>
          <enabled>true</enabled>
        </matlabcoder>
      </toolbox>
      <toolbox>
        <matlabhdlcoder>
          <enabled>true</enabled>
        </matlabhdlcoder>
      </toolbox>
      <toolbox>
        <embeddedcoder>
          <enabled>true</enabled>
        </embeddedcoder>
      </toolbox>
    </matlab>
    <platform>
      <unix>true</unix>
      <mac>true</mac>
      <windows>false</windows>
      <win2k>false</win2k>
      <winxp>false</winxp>
      <vista>false</vista>
      <linux>false</linux>
      <solaris>false</solaris>
      <osver>10.13.5</osver>
      <os32>false</os32>
      <os64>true</os64>
      <arch>maci64</arch>
      <matlab>true</matlab>
    </platform>
  </configuration>
</deployment-project>